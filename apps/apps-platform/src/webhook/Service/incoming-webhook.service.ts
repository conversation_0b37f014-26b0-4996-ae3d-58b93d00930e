import { BadRequestException, Inject, Injectable } from "@nestjs/common";
import { AuthenticationGrpcClient } from "@repo/nestjs-commons/guards";
import { ILogger } from "@repo/nestjs-commons/logger";
import { ContextUserType, SNSPublisherService } from "@repo/thena-eventbridge";
import { CachedAppInstallationsRepository } from "@repo/thena-platform-entities";
import Ajv from "ajv";
import { v4 as uuidv4 } from "uuid";
import { ConfigService } from "../../config/app.config.service";
import { BaseWebhookEvent } from "../dtos/incoming-webhook.dto";
import { SchemaProperty } from "../Interfaces/incoming-webhook.interface";
@Injectable()
export class IncomingWebhookService {
  private readonly ajv: Ajv;
  constructor(
    @Inject("CustomLogger") private readonly logger: ILogger,
    @Inject("WEBHOOK_SNS_PUBLISHER")
    private readonly snsPublisherService: SNSPublisherService,
    private readonly authenticationGrpcClient: AuthenticationGrpcClient,
    private readonly cachedAppInstallationsRepository: CachedAppInstallationsRepository,
    private readonly configService: ConfigService,
  ) {
    this.ajv = new Ajv({ allErrors: true });
  }

  /**
   * Handles incoming webhook events after validating the API key
   * @param body The webhook payload
   * @param apiKey The API key to validate
   * @returns The webhook response
   */
  async handlePublishedEventsWebhook(body: BaseWebhookEvent, botKey: string) {
    try {
      const user = await this.authenticationGrpcClient.validateKey(botKey);
      const { uid, isPrivilegedApp = false } = user;

      // Check if this is a privileged app (for future use)
      if (isPrivilegedApp) {
        this.logger.log(
          `APP-PLATFORM-INFO[handlePublishedEventsWebhook]: Processing event from privileged app for bot: ${uid}`,
        );
      } else {
        this.logger.debug(
          `APP-PLATFORM-INFO[handlePublishedEventsWebhook]: Processing event from regular app for bot: ${uid}`,
        );
      }
      /**
       * The user.uid is the bot user uid for whome the event.published event is being pushed.
       * Step 1: Fetch the bot details from the database.
       * Step 2: Validate the event type.
       * Step 3: Process the event.
       */

      const installation =
        await this.cachedAppInstallationsRepository.findByCondition({
          where: {
            botUserId: uid,
          },
          relations: ["app"],
        });

      if (!installation) {
        throw new BadRequestException("Installation not found");
      }

      const publishedEvents =
        installation?.app?.manifest?.events?.publish || null;
      if (!publishedEvents) {
        throw new BadRequestException("Published event not found");
      }

      // Find the event configuration that matches the incoming event
      const eventConfig = publishedEvents.find(
        (event) => event.event === body.event_name,
      );

      if (!eventConfig) {
        throw new BadRequestException(
          `Event: '${body.event_name}' is not configured for this application`,
        );
      }

      // Get all properties from the schema as required fields
      const requiredFields = Object.keys(eventConfig.schema.properties || {});

      // Add required fields to schema and prevent additional properties
      const validationSchema = {
        ...eventConfig.schema,
        required: requiredFields,
        additionalProperties: false,
      };

      // Validate the payload against the event schema
      const validate = this.ajv.compile(validationSchema);
      const isValid = validate(body.payload);

      if (!isValid) {
        const errors = validate.errors || [];
        const missingFields = errors
          .filter((err) => err.keyword === "required")
          .map((err) => err.params.missingProperty);

        const enumErrors = errors
          .filter((err) => err.keyword === "enum")
          .map(
            (err) =>
              `${err.instancePath.slice(
                1,
              )} must be one of: ${err.params.allowedValues.join(", ")}`,
          );

        const formatPropertyDescription = (
          property: SchemaProperty,
        ): string => {
          if (property.enum) {
            return `one of: [${property.enum.join(", ")}]`;
          }
          return `type: ${property.type}`;
        };

        const expectedPayloadFormat = Object.entries(
          eventConfig.schema.properties,
        ).reduce((formattedProps, [fieldName, fieldSchema]) => {
          formattedProps[fieldName] = formatPropertyDescription(
            fieldSchema as SchemaProperty,
          );
          return formattedProps;
        }, {});

        const errorMessage = {
          message: "Invalid webhook payload",
          errors: [...missingFields, ...enumErrors],
          expectedFormat: {
            event_name: eventConfig.event,
            payload: expectedPayloadFormat,
          },
        };

        throw new BadRequestException({
          message: "Invalid webhook payload",
          details: errorMessage.errors,
          example: errorMessage.expectedFormat,
        });
      }

      /**
       * Publish the event to the SNS topic
       * Workflows and other modules that depend on this event should subscribe to the SNS topic.
       */
      try {
        this.logger.log(
          `APP-PLATFORM-INFO[handlePublishedEventsWebhook]: Processing ${body.event_name} event for bot: ${uid}`,
        );
        await this.publishToSNS({
          event: body.event_name,
          payload: body.payload,
          botUserId: uid,
          organizationId: installation.app.organizationId,
          contextUserType: ContextUserType.BOT_USER,
        });
        this.logger.log(
          `APP-PLATFORM-INFO[handlePublishedEventsWebhook]: Published ${body.event_name} event for bot: ${uid}`,
        );
      } catch (error) {
        this.logger.error(
          "APP-PLATFORM-ERROR[handlePublishedEventsWebhook]:",
          error,
        );
        throw new BadRequestException(error);
      }

      return {
        success: true,
        messageId: "12345678-1234-1234-1234-123456789012",
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        "APP-PLATFORM-ERROR[handlePublishedEventsWebhook]:",
        error,
      );
      throw new BadRequestException(error);
    }
  }

  private async publishToSNS(params: {
    event: string;
    payload: Record<string, any>;
    botUserId: string;
    organizationId: string;
    contextUserType: ContextUserType;
  }) {
    const { event, payload, botUserId, organizationId, contextUserType } =
      params;
    try {
      const eventId = uuidv4();
      await this.snsPublisherService.publishSNSMessage({
        topicArn: this.configService.get("AWS_SNS_WEBHOOK_TOPIC_ARN"),
        subject: event,
        message: JSON.stringify(payload),
        messageAttributes: {
          event_name: event,
          event_id: eventId,
          event_timestamp: Math.floor(Date.now() / 1000).toString(),
          context_user_id: botUserId,
          context_organization_id: organizationId,
          context_user_type: contextUserType,
        },
      });
      this.logger.log(
        `APP-PLATFORM-INFO[publishToSNS]: EVENT_ID: ${eventId} | Published ${params.event} event for bot: ${params.botUserId}`,
      );
    } catch (error) {
      this.logger.error("APP-PLATFORM-ERROR[publishToSNS]:", error);
      throw new BadRequestException(error);
    }
  }
}
