import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import {
    ApiKey,
    AppInstallation,
    User
} from "@repo/thena-platform-entities";
import { Repository } from "typeorm";

export interface PrivilegedAppValidationResult {
  isValid: boolean;
  user?: User;
  error?: string;
  metadata?: {
    originalBotUserId: string;
    impersonatedUserId: string;
    appId: string;
    organizationId: string;
  };
}

@Injectable()
export class PrivilegedAppService {
  private readonly logger = new Logger(PrivilegedAppService.name);

  constructor(
    @InjectRepository(ApiKey)
    private readonly apiKeyRepository: Repository<ApiKey>,
    @InjectRepository(AppInstallation)
    private readonly appInstallationRepository: Repository<AppInstallation>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Checks if an API key belongs to a privileged app
   * @param apiKey The full API key (keyId.keySecret)
   * @returns Promise<boolean> indicating if the app is privileged
   */
  async isPrivilegedApp(apiKey: string): Promise<boolean> {
    try {
      const [keyId] = apiKey.split('.');
      if (!keyId) {
        this.logger.debug('Invalid API key format for privileged app check', {
          apiKey: apiKey.substring(0, 10) + '...',
        });
        return false;
      }

      // Find the API key and join with user, app installation, and app
      const apiKeyRecord = await this.apiKeyRepository
        .createQueryBuilder('apiKey')
        .leftJoinAndSelect('apiKey.user', 'user')
        .leftJoin('app_installations', 'appInstallation', 'appInstallation.botUserId = user.uid')
        .leftJoin('apps', 'app', 'app.id = appInstallation.appId')
        .where('apiKey.keyId = :keyId', { keyId })
        .andWhere('apiKey.isActive = :isActive', { isActive: true })
        .andWhere('app.isThenaPrivileged = :isPrivileged', { isPrivileged: true })
        .andWhere('appInstallation.status = :status', { status: 'active' })
        .select(['apiKey.id', 'user.id', 'app.isThenaPrivileged'])
        .getOne();

      const isPrivileged = !!apiKeyRecord;
      this.logger.debug('Privileged app check result', {
        keyId,
        isPrivileged,
        hasApiKeyRecord: !!apiKeyRecord,
      });

      return isPrivileged;
    } catch (error) {
      this.logger.error('Error checking if app is privileged', {
        error: error.message,
        apiKey: apiKey.substring(0, 10) + '...',
      });
      return false;
    }
  }

  /**
   * Validates user impersonation for a privileged app
   * @param apiKey The full API key
   * @param userId The user ID to impersonate
   * @returns Promise<PrivilegedAppValidationResult>
   */
  async getUserInSameOrganization(
    apiKey: string,
    userId: string,
  ): Promise<PrivilegedAppValidationResult> {
    try {
      this.logger.debug('Starting privileged app user validation', {
        userId,
        apiKey: apiKey.substring(0, 10) + '...',
      });

      const [keyId, keySecret] = apiKey.split('.');

      if (!keyId || !keySecret) {
        this.logger.debug('Invalid API key format during user validation', {
          hasKeyId: !!keyId,
          hasKeySecret: !!keySecret,
        });
        return {
          isValid: false,
          error: 'Invalid API key format',
        };
      }

      // First, get the API key and bot user information
      const apiKeyRecord = await this.apiKeyRepository
        .createQueryBuilder('apiKey')
        .leftJoinAndSelect('apiKey.user', 'botUser')
        .leftJoinAndSelect('botUser.organization', 'botOrganization')
        .where('apiKey.keyId = :keyId', { keyId })
        .andWhere('apiKey.isActive = :isActive', { isActive: true })
        .getOne();

      if (!apiKeyRecord) {
        this.logger.debug('API key not found or inactive', { keyId });
        return {
          isValid: false,
          error: 'Invalid API key',
        };
      }

      this.logger.debug('Found API key record', {
        keyId,
        botUserId: apiKeyRecord.user.uid,
        botOrgId: apiKeyRecord.user.organizationId,
      });

      // Check if the API key belongs to a privileged app
      const isPrivileged = await this.isPrivilegedApp(apiKey);
      if (!isPrivileged) {
        this.logger.debug('API key does not belong to privileged app', {
          keyId,
          botUserId: apiKeyRecord.user.uid,
        });
        return {
          isValid: false,
          error: 'API key does not belong to a privileged app',
        };
      }

      // Find the target user to impersonate
      const targetUser = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.organization', 'organization')
        .where('user.id = :userId', { userId })
        .orWhere('user.uid = :userId', { userId })
        .getOne();

      if (!targetUser) {
        this.logger.debug('Target user not found', {
          userId,
          keyId,
        });
        return {
          isValid: false,
          error: 'Invalid user ID or user not found in organization',
        };
      }

      this.logger.debug('Found target user', {
        targetUserId: targetUser.uid,
        targetUserOrgId: targetUser.organizationId,
        botOrgId: apiKeyRecord.user.organizationId,
      });

      // Verify both users are in the same organization
      if (targetUser.organizationId !== apiKeyRecord.user.organizationId) {
        this.logger.warn('Organization mismatch in privileged app impersonation', {
          targetUserOrgId: targetUser.organizationId,
          botOrgId: apiKeyRecord.user.organizationId,
          targetUserId: targetUser.uid,
          botUserId: apiKeyRecord.user.uid,
        });
        return {
          isValid: false,
          error: 'Invalid user ID or user not found in organization',
        };
      }

      // Get app information for metadata
      const appInstallation = await this.appInstallationRepository
        .createQueryBuilder('appInstallation')
        .leftJoinAndSelect('appInstallation.app', 'app')
        .where('appInstallation.botUserId = :botUserId', {
          botUserId: apiKeyRecord.user.uid
        })
        .andWhere('appInstallation.organizationId = :organizationId', {
          organizationId: apiKeyRecord.user.organizationId
        })
        .andWhere('appInstallation.status = :status', { status: 'active' })
        .getOne();

      if (!appInstallation) {
        this.logger.debug('App installation not found or inactive', {
          botUserId: apiKeyRecord.user.uid,
          organizationId: apiKeyRecord.user.organizationId,
        });
        return {
          isValid: false,
          error: 'App installation is not active',
        };
      }

      this.logger.debug('Privileged app user validation successful', {
        appId: appInstallation.app.id,
        botUserId: apiKeyRecord.user.uid,
        targetUserId: targetUser.uid,
        organizationId: targetUser.organizationId,
      });

      return {
        isValid: true,
        user: targetUser,
        metadata: {
          originalBotUserId: apiKeyRecord.user.id,
          impersonatedUserId: targetUser.id,
          appId: appInstallation.app.id,
          organizationId: targetUser.organizationId,
        },
      };
    } catch (error) {
      this.logger.error('Error validating user impersonation', {
        error: error.message,
        userId,
        apiKey: apiKey.substring(0, 10) + '...',
      });

      return {
        isValid: false,
        error: 'Internal server error during validation',
      };
    }
  }
}
