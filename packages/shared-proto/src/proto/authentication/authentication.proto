syntax = "proto3";

package grpc.authentication.v1;

// Authentication service
service AuthenticationService {
  // Sign in
  rpc SignIn(SignInRequest) returns (SignInResponse);

  // Validate and get user
  rpc ValidateAndGetUser(ValidateAndGetUserRequest) returns (ValidateAndGetUserResponse);

	// Validate and get user by api key
	rpc ValidateAndGetUserByApiKey(ValidateAndGetUserByApiKeyRequest) returns (ValidateAndGetUserByApiKeyResponse);

  // Create new api key
  rpc CreateNewAPIKey(CreateNewAPIKeyRequest) returns (CreateNewAPIKeyResponse);

  // Create user
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);

  // Create organization admin
  rpc CreateOrganizationAdmin(CreateOrganizationAdminRequest) returns (CreateOrganizationAdminResponse);

  // Create organization admin
  rpc CreateBotUser(CreateBotUserRequest) returns (CreateBotUserResponse);

  // Get supabase auth user
  rpc GetSupabaseAuthUser(GetSupabaseAuthUserRequest) returns (GetSupabaseAuthUserResponse);

	// Create user without auth
	rpc CreateUserWithoutAuth(CreateUserWithoutAuthRequest) returns (CreateUserWithoutAuthResponse);

  // Validate and get user by internal IDs
  rpc ValidateAndGetUserByInternalIds(ValidateAndGetUserByInternalIdsRequest) returns (ValidateAndGetUserByInternalIdsResponse);
}

message CreateNewAPIKeyRequest {
  string name = 1;
  string description = 2;
  optional string expiresAt = 3;
}

message CreateNewAPIKeyResponse {
  string apiKey = 1;
  string secretKey = 2;
}

/* The validate and get user by api key request */
message ValidateAndGetUserByApiKeyRequest {
  string api_key = 1;
}

/* The validate and get user by api key response */
message ValidateAndGetUserByApiKeyResponse {
  string sub = 1; // The user id
  string auth_id = 2; // The auth id
  string uid = 3; // The user id same as the sub
  string email = 4; // The user email
  string org_id = 5; // The organization id
  string org_uid = 6; // The organization uid
  string user_type = 7; // The user type
  string timezone = 8; // The user timezone
  repeated string scopes = 9; // The user scopes
  OrganizationTier orgTier = 10;
  bool is_privileged_app = 11; // Whether this user belongs to a privileged app
}

message SignInRequest {
  string email = 1;
  string password = 2;
  optional string organization_id = 3;
}

message SignInResponse {
  string token = 1;
  string refresh_token = 2;
}

enum UserType {
  ORG_ADMIN = 0;
  USER = 1;
  CUSTOMER_USER = 2;
  APP_USER = 3;
  BOT_USER = 4;
}

enum OrganizationTier {
  FREE = 0;
  STANDARD = 1;
  ENTERPRISE = 2;
}

message GetSupabaseAuthUserRequest {}

message GetSupabaseAuthUserResponse {
  string id = 1;
  string email = 2;
  string phone = 3;
  string created_at = 4;
  string updated_at = 5;
}

message CreateOrganizationAdminRequest {
  string email = 1;
  string password = 2;
  string organization_uid = 3;
}

message CreateOrganizationAdminResponse {
  string id = 1;
  string name = 2;
  string email = 3;
  string user_type = 4;
}

message CreateBotUserRequest {
  string name = 1;
  string email = 2;
  string password = 3;
  string organization_uid = 4;
  optional string app_id = 5;
  optional string purpose = 6;
  optional string avatar_url = 7;
}

message CreateBotUserResponse {
  string id = 1;
  string name = 2;
  string email = 3;
  string user_type = 4;
  string token = 5;
  string refresh_token = 6;
}

message CreateUserRequest {
  string name = 1;
  string email = 2;
  string password = 3;
  string organization_uid = 4;
  optional bool is_customer = 5;
  optional string external_id = 6;
}

message CreateUserWithoutAuthRequest {
  string name = 1;
  string email = 2;
  string password = 3;
  string organization_uid = 4;
  optional bool is_customer = 5;
  optional string external_id = 6;
}

message CreateUserWithoutAuthResponse {
  string id = 1;
  string name = 2;
  string email = 3;
  string user_type = 4;
}


message CreateUserResponse {
  string id = 1;
  string name = 2;
  string email = 3;
  string user_type = 4;
}

message ValidateAndGetUserRequest {}

message ValidateAndGetUserResponse {
  string sub = 1;
  string auth_id = 2;
  string uid = 3;
  string email = 4;
  string org_id = 5;
  string org_uid = 6;
  string user_type = 7;
  string timezone = 8;
  repeated string scopes = 9;
  OrganizationTier orgTier = 10;
}

// Add these new message types to your proto file
message ValidateAndGetUserByInternalIdsRequest {
  string user_id = 1;
  string org_id = 2;
}

message ValidateAndGetUserByInternalIdsResponse {
  string sub = 1;
  string auth_id = 2;
  string uid = 3;
  string email = 4;
  string org_id = 5;
  string org_uid = 6;
  string user_type = 7;
  string timezone = 8;
  repeated string scopes = 9;
  OrganizationTier orgTier = 10;
}
